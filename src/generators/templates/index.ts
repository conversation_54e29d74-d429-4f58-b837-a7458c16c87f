import { echidnaConfigTemplate } from './echidna-config';
import { halmosConfigTemplate } from './halmos-config';
import { medusaConfigTemplate } from './medusa-config';
import { beforeAfterTemplate } from './before-after';
import { cryticTesterTemplate } from './crytic-tester';
import { cryticToFoundryTemplate } from './crytic-to-foundry';
import { propertiesTemplate } from './properties';
import { setupTemplate } from './setup';
import { targetFunctionsTemplate } from './target-functions';
import { adminTargetsTemplate } from './targets/admin-targets';
import { doomsdayTargetsTemplate } from './targets/doomsday-targets';
import { managersTargetsTemplate } from './targets/managers-targets';
import { targetsTemplate } from './targets/targets';

export {
    echidnaConfigTemplate,
    halmosConfigTemplate,
    medusaConfigTemplate,
    beforeAfterTemplate,
    cryticTesterTemplate,
    cryticToFoundryTemplate,
    propertiesTemplate,
    setupTemplate,
    targetFunctionsTemplate,
    adminTargetsTemplate,
    doomsdayTargetsTemplate,
    managersTargetsTemplate,
    targetsTemplate,
};