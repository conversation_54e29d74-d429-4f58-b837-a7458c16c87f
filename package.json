{"name": "recon", "displayName": "Recon", "description": "Seamless integration of Foundry, Medusa, Echidna, and Halmos for smart contract testing", "version": "0.0.26", "license": "GPL-2.0", "publisher": "Recon-Fuzz", "repository": {"type": "git", "url": "https://github.com/Recon-Fuzz/recon-extension.git"}, "icon": "images/icon.png", "engines": {"vscode": "^1.88.0"}, "categories": ["Other"], "activationEvents": ["workspaceContains:foundry.toml"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "recon-cockpit", "title": "Recon", "icon": "images/icon.svg"}]}, "views": {"recon-cockpit": [{"type": "webview", "id": "recon-contracts", "name": "Contracts"}, {"type": "webview", "id": "recon-main", "name": "Recon Cockpit"}, {"type": "webview", "id": "recon-coverage", "name": "Coverage Reports"}]}, "submenus": [{"id": "recon.tools", "label": "Tools", "icon": "$(tools)"}], "menus": {"view/item/context": [{"command": "recon.clone", "when": "view == recon-c4-ongoing", "group": "inline"}], "view/title": [{"command": "recon.showAllFiles", "when": "view == recon-contracts && !recon.showingAllFiles", "group": "navigation"}, {"command": "recon.refreshContracts", "when": "view == recon-contracts", "group": "navigation"}, {"command": "recon.hideAllFiles", "when": "view == recon-contracts && recon.showingAllFiles", "group": "navigation"}, {"command": "recon.refreshCoverage", "when": "view == recon-coverage", "group": "navigation"}, {"submenu": "recon.tools", "when": "view == recon-main", "group": "navigation"}], "explorer/context": [{"command": "recon.generateMock", "when": "resourceExtname == .json || resourceExtname == .sol", "group": "2_workspace"}, {"command": "recon.generateTargetFunctions", "when": "resourceExtname == .json || resourceExtname == .sol", "group": "2_workspace"}, {"command": "recon.cleanupCoverageReport", "when": "resourceExtname == .html && (resourcePath =~ /echidna/ || resourcePath =~ /medusa/ || resourcePath =~ /halmos/)", "group": "2_workspace"}], "recon-cockpit": [{"submenu": "recon.tools", "group": "navigation"}], "recon.tools": [{"command": "recon.logToFoundry", "group": "1_conversion"}]}, "commands": [{"command": "recon.helloWorld", "title": "Hello World"}, {"command": "recon.clone", "title": "<PERSON><PERSON>", "icon": "images/icon.png"}, {"command": "recon.installChimera", "title": "Install Chimera", "icon": "$(cloud-download)"}, {"command": "recon.toggleContract", "title": "Toggle Contract", "icon": "$(check)"}, {"command": "recon.toggleFunction", "title": "Toggle Function", "icon": "$(check)"}, {"command": "recon.showAllFiles", "title": "Show All Files", "icon": "$(eye)"}, {"command": "recon.hideAllFiles", "title": "Hide Test & Library Files", "icon": "$(eye-closed)"}, {"command": "recon.refreshContracts", "title": "Refresh Contracts", "icon": "$(refresh)"}, {"command": "recon.buildProject", "title": "Forge Build", "category": "Recon"}, {"command": "recon.runEchidna", "title": "Run Echidna", "category": "Recon"}, {"command": "recon.runMedusa", "title": "Run Medusa", "category": "Recon"}, {"command": "recon.runHalmos", "title": "<PERSON>", "category": "Recon"}, {"command": "recon.runTest", "title": "Run Solidity Test", "category": "Recon"}, {"command": "recon.debugTest", "title": "Debug Solidity Test", "category": "Recon"}, {"command": "recon.setFunctionMode", "title": "Set Function Mode", "category": "Recon"}, {"command": "recon.setFunctionActor", "title": "Set Function Actor", "category": "Recon"}, {"command": "recon.generateMock", "title": "Generate Solidity Mock", "category": "Recon"}, {"command": "recon.cleanupCoverageReport", "title": "Clean up Coverage Report", "category": "Recon"}, {"command": "recon.refreshCoverage", "title": "Refresh Coverage", "icon": "$(refresh)"}, {"command": "recon.generateTargetFunctions", "title": "Generate Target Functions", "category": "Recon"}, {"command": "recon.logToFoundry", "title": "Log to Foundry", "category": "Recon"}], "configuration": {"title": "Recon", "properties": {"recon.defaultFuzzer": {"type": "string", "default": "Echidna", "scope": "resource", "enum": ["Echidna", "Medusa", "Hal<PERSON>"], "description": "Default fuzzing tool to use", "order": 1}, "recon.showAllFiles": {"type": "boolean", "default": false, "scope": "resource", "order": 2, "description": "Show all contract files including tests and libraries"}, "recon.echidna.mode": {"type": "string", "scope": "resource", "order": 3, "default": "assertion", "enum": ["property", "assertion", "optimization", "overflow", "exploration"], "description": "Execution mode for Echidna"}, "recon.echidna.testLimit": {"type": "number", "default": 1000000, "minimum": 1, "scope": "resource", "order": 4, "description": "Test limit for Echidna fuzzing"}, "recon.echidna.workers": {"type": "number", "default": 8, "minimum": 1, "scope": "resource", "order": 5, "description": "Number of workers for Echidna fuzzing"}, "recon.medusa.testLimit": {"type": "number", "default": 0, "minimum": 0, "scope": "resource", "order": 6, "description": "Test limit for Medusa fuzzing"}, "recon.medusa.workers": {"type": "number", "default": 10, "minimum": 1, "scope": "resource", "order": 7, "description": "Number of workers for Medusa fuzzing"}, "recon.halmos.depth": {"type": "number", "default": 22, "minimum": 1, "scope": "resource", "order": 8, "description": "Maximum call depth for Halmos symbolic execution"}, "recon.halmos.unroll": {"type": "number", "default": 256, "minimum": 1, "scope": "resource", "order": 9, "description": "Maximum loop unrolling factor for Halmos"}, "recon.halmos.solver": {"type": "string", "default": "z3", "scope": "resource", "enum": ["z3", "cvc5", "bitwuzla", "yices"], "order": 10, "description": "SMT solver to use for Halmos symbolic execution"}, "recon.foundryConfigPath": {"type": "string", "default": "foundry.toml", "description": "Path to foundry.toml configuration file (relative to workspace root)", "scope": "resource", "order": 11}, "recon.forge.buildArgs": {"type": "string", "default": "", "description": "Extra arguments to pass to forge build command (e.g. --via-ir)", "scope": "resource", "order": 12}, "recon.forge.testVerbosity": {"type": "string", "default": "-vvv", "enum": ["-v", "-vv", "-vvv", "-vvvv", "-vvvvv"], "description": "Verbosity level for forge test command", "scope": "resource", "order": 13}, "recon.mocksFolderPath": {"type": "string", "default": "test/recon/mocks", "description": "Path to store generated mock contracts (relative to foundry.toml location)", "scope": "resource", "order": 14}, "recon.mockAutoSave": {"type": "boolean", "default": true, "description": "Automatically save generated mock contracts to disk", "scope": "resource", "order": 15}, "recon.customTestFolderPath": {"type": "string", "default": "", "description": "Custom path for test files (relative to foundry.toml location). If empty, standard paths will be searched", "scope": "resource", "order": 16}}}}, "scripts": {"vscode:prepublish": "yarn run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "yarn run compile && yarn run lint", "lint": "eslint src", "test": "vscode-test", "package": "vsce package", "prepare": "yarn run compile"}, "devDependencies": {"@types/jsdom": "^21.1.7", "@types/mocha": "^10.0.10", "@types/node": "24.0.4", "@types/vscode": "^1.88.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "eslint": "^9.29.0", "typescript": "^5.8.3"}, "dependencies": {"@recon-fuzz/log-parser": "^0.0.33", "@solidity-parser/parser": "^0.20.1", "@types/prismjs": "^1.26.5", "@vscode/codicons": "^0.0.36", "@vscode/webview-ui-toolkit": "^1.4.0", "abi-to-mock": "^1.0.11", "case": "^1.6.3", "echidna-coverage-parser": "^1.0.1", "handlebars": "^4.7.8", "jsdom": "^26.1.0", "prismjs": "^1.30.0", "yaml": "^2.8.0"}}