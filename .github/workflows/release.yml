name: Build and Release Extension

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write
  discussions: write

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org/'
  
      - name: Setup npm credentials
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          echo "@recon-fuzz:registry=https://registry.npmjs.org/" > .npmrc
          echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" >> .npmrc
  
      - name: Install dependencies
        run: yarn install
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      
      - name: Install vsce
        run: npm install -g @vscode/vsce
        
      - name: Build extension
        run: vsce package
        
      - name: Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: "*.vsix"
          token: ${{ secrets.GITHUB_TOKEN }}
          generateReleaseNotes: true