# Changelog

## [0.0.25] - 25.06.2025

### Added
- Log to Foundry Repro Converter

### Fixed
- Rearrange the buttons for better UX
- Upgrade dependencies

## [0.0.24] - 29.05.2025

### Fixed
- Custom target contract
- Custom src folder handling
- Fallback/Receive functions mock
- Payable functions mock

## [0.0.23] - 26.05.2025

### Fixed
- Load the PATH env variable correctly

## [0.0.22] - 15.05.2025

### Fixed
- Waiting for Medusa process to exit completely (with maximum of 60 seconds)
- Persistant recon.json

## [0.0.21] - 15.05.2025

### Fixed
- Waiting for Echidna process to exit completely (with maximum of 60 seconds)
- Upgrade packages to latest versions

## [0.0.20] - 12.05.2025

### Fixed
- Issue with PATH - (h/t: @<PERSON><PERSON>enenbaum)
- Overloaded functions in mock (functions with same name)
- Memory issue because of output folder watchers
- Better Echidna repro (using shrunken version)
- Refresh contracts button

## [0.0.19] - 17.04.2025

### Fixed
- Contract watcher now correctly tracks individual file changes in output folder

## [0.0.18] - 17.04.2025

### Fixed
- Coverage report compatibility with new Medusa report format
- Issue with fuzzer not stopping

[0.0.25]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.25
[0.0.24]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.24
[0.0.23]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.23
[0.0.22]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.22
[0.0.21]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.21
[0.0.20]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.20
[0.0.19]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.19
[0.0.18]: https://github.com/Recon-Fuzz/recon-extension/releases/tag/v0.0.18